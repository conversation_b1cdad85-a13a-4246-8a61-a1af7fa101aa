<?php
/**
 * Role Custom Debug - Rol Kontrol Dosyası
 * 
 * Bu dosyayı WordPress root dizininde çalıştırarak Superole rolünün
 * oluşturulup oluşturulmadığını kontrol edebilirsiniz.
 * 
 * Kullanım: WordPress root dizininde bu dosyayı çalıştırın
 * Örnek: php debug-role-check.php
 */

// WordPress'i yükle
if (file_exists('./wp-config.php')) {
    require_once('./wp-config.php');
    require_once('./wp-load.php');
} else {
    die("WordPress bulunamadı! Bu dosyayı WordPress root dizininde çalıştırın.\n");
}

echo "=== Role Custom Debug - Rol Kontrol ===\n\n";

// 1. Eklenti aktif mi kontrol et
$active_plugins = get_option('active_plugins', []);
$plugin_active = false;
foreach ($active_plugins as $plugin) {
    if (strpos($plugin, 'role-custom') !== false) {
        $plugin_active = true;
        break;
    }
}

echo "1. Eklenti Durumu: " . ($plugin_active ? "✓ Aktif" : "✗ Aktif değil") . "\n";

// 2. Role Custom sınıfı yüklü mü?
$class_exists = class_exists('Role_Custom');
echo "2. Role_Custom Sınıfı: " . ($class_exists ? "✓ Yüklü" : "✗ Yüklü değil") . "\n";

// 3. Superole rolü var mı?
$superole_role = get_role('superole');
echo "3. Superole Rolü: " . ($superole_role ? "✓ Mevcut" : "✗ Mevcut değil") . "\n";

if ($superole_role) {
    echo "   - Yetki Sayısı: " . count($superole_role->capabilities) . "\n";
    echo "   - Temel Yetkiler:\n";
    
    $important_caps = [
        'read' => 'Okuma',
        'manage_tutor' => 'Tutor Yönetimi (Öğrenciler için gerekli)',
        'manage_tutor_instructor' => 'Tutor Eğitmen Yönetimi',
        'manage_woocommerce' => 'WooCommerce Yönetimi',
        'view_woocommerce_reports' => 'WooCommerce Raporları',
        'edit_shop_orders' => 'Sipariş Düzenleme'
    ];
    
    foreach ($important_caps as $cap => $desc) {
        $has_cap = $superole_role->has_cap($cap);
        echo "     - {$desc}: " . ($has_cap ? "✓" : "✗") . "\n";
    }
}

// 4. Tüm rolleri listele
echo "\n4. Tüm WordPress Rolleri:\n";
$all_roles = wp_roles()->get_names();
foreach ($all_roles as $role_key => $role_name) {
    $marker = ($role_key === 'superole') ? " ← SUPEROLE" : "";
    echo "   - {$role_key}: {$role_name}{$marker}\n";
}

// 5. Superole rolüne sahip kullanıcılar
echo "\n5. Superole Rolüne Sahip Kullanıcılar:\n";
$superole_users = get_users(['role' => 'superole']);
if (empty($superole_users)) {
    echo "   - Henüz hiçbir kullanıcı Superole rolüne sahip değil.\n";
} else {
    foreach ($superole_users as $user) {
        echo "   - {$user->display_name} ({$user->user_login})\n";
    }
}

// 6. Manuel rol oluşturma testi
echo "\n6. Manuel Rol Oluşturma Testi:\n";
if (!$superole_role) {
    echo "   Superole rolü bulunamadı, manuel olarak oluşturuluyor...\n";
    
    // Temel yetkiler
    $capabilities = [
        'read' => true,
        'manage_tutor' => true,              // Öğrenciler menüsü için gerekli
        'manage_tutor_instructor' => true,
        'tutor_instructor' => true,
        'manage_woocommerce' => true,
        'view_woocommerce_reports' => true,
        'edit_shop_orders' => true,
        'edit_others_shop_orders' => true,
    ];
    
    $result = add_role('superole', 'Superole', $capabilities);
    
    if ($result) {
        echo "   ✓ Superole rolü başarıyla oluşturuldu!\n";
        
        // WordPress roles cache'ini temizle
        if (function_exists('wp_roles')) {
            wp_roles()->reinit();
        }
        
        // Global $wp_roles değişkenini sıfırla
        global $wp_roles;
        if (isset($wp_roles)) {
            $wp_roles = null;
        }
        
        echo "   ✓ Roles cache temizlendi.\n";
        
        // Tekrar kontrol et
        $new_role = get_role('superole');
        echo "   ✓ Kontrol: " . ($new_role ? "Rol başarıyla oluşturuldu" : "Rol oluşturulamadı") . "\n";
        
    } else {
        echo "   ✗ Superole rolü oluşturulamadı!\n";
    }
} else {
    echo "   ✓ Superole rolü zaten mevcut.\n";
}

// 7. WordPress veritabanı kontrol
echo "\n7. Veritabanı Kontrol:\n";
global $wpdb;
$roles_option = get_option($wpdb->prefix . 'user_roles');
if ($roles_option && isset($roles_option['superole'])) {
    echo "   ✓ Superole rolü veritabanında mevcut.\n";
    echo "   - Veritabanındaki yetki sayısı: " . count($roles_option['superole']['capabilities']) . "\n";
} else {
    echo "   ✗ Superole rolü veritabanında bulunamadı.\n";
    echo "   - Mevcut roller: " . implode(', ', array_keys($roles_option ?: [])) . "\n";
}

// 8. Tutor LMS Menü Kontrolü
echo "\n8. Tutor LMS Menü Durumu:\n";
if (class_exists('TUTOR\Admin')) {
    echo "   ✓ Tutor LMS Admin sınıfı mevcut.\n";

    // İzin verilen menüler
    $allowed_menus = [
        'tutor' => 'Kurslar',
        'tutor-students' => 'Öğrenciler',
        'tutor_announcements' => 'Duyurular',
        'question_answer' => 'Q&A',
        'tutor_quiz_attempts' => 'Sınav Denemeleri'
    ];

    echo "   İzin verilen Tutor LMS menüleri:\n";
    foreach ($allowed_menus as $slug => $name) {
        echo "     - {$name} ({$slug})\n";
    }

    // Öğrenciler menüsü için özel kontrol
    if ($superole_role && $superole_role->has_cap('manage_tutor')) {
        echo "   ✓ Superole rolü 'manage_tutor' yetkisine sahip (Öğrenciler menüsü için gerekli).\n";
    } else {
        echo "   ✗ Superole rolü 'manage_tutor' yetkisine sahip değil!\n";
    }
} else {
    echo "   ✗ Tutor LMS bulunamadı.\n";
}

// 9. Öneriler
echo "\n9. Sorun Giderme Önerileri:\n";
if (!$plugin_active) {
    echo "   - Role Custom eklentisini WordPress admin panelinden etkinleştirin\n";
}
if (!$superole_role) {
    echo "   - Eklentiyi devre dışı bırakıp tekrar etkinleştirin\n";
    echo "   - WordPress admin panelinde Kullanıcılar > Tüm Kullanıcılar sayfasını yenileyin\n";
    echo "   - Tarayıcı cache'ini temizleyin\n";
}
if ($superole_role && !$superole_role->has_cap('manage_tutor')) {
    echo "   - Eklentiyi devre dışı bırakıp tekrar etkinleştirin (manage_tutor yetkisi eksik)\n";
}

echo "\n=== Debug Tamamlandı ===\n";
?>
