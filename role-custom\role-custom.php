<?php
/**
 * Plugin Name: Role Custom
 * Plugin URI: https://example.com/role-custom
 * Description: WordPress eklentisi - Superole rolü oluşturur ve Tutor LMS menülerini kısıtlar, WooCommerce'e tam eri<PERSON><PERSON>.
 * Version: 1.0.0
 * Author: Role Custom Developer
 * Author URI: https://example.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: role-custom
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 */

// Doğrudan erişimi engelle
if (!defined('ABSPATH')) {
    exit;
}

// Eklenti sabitleri
define('ROLE_CUSTOM_VERSION', '1.0.0');
define('ROLE_CUSTOM_PLUGIN_FILE', __FILE__);
define('ROLE_CUSTOM_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('ROLE_CUSTOM_PLUGIN_URL', plugin_dir_url(__FILE__));
define('ROLE_CUSTOM_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Ana Role Custom sınıfı
 */
class Role_Custom {
    
    /**
     * Singleton instance
     */
    private static $instance = null;
    
    /**
     * Superole rol adı
     */
    const SUPEROLE_ROLE = 'superole';
    
    /**
     * Tutor LMS'de izin verilen menüler
     */
    private $allowed_tutor_menus = [
        'tutor',                           // Kurslar (ana sayfa)
        'tutor-students',                  // Öğrenciler
        'tutor_announcements',             // Duyurular
        'question_answer',                 // Q&A
        'tutor_quiz_attempts'              // Sınav Denemeleri
    ];
    
    /**
     * Singleton pattern
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Hook'ları başlat
     */
    private function init_hooks() {
        // Admin hook'ları
        add_action('admin_menu', [$this, 'restrict_tutor_menus'], 999);
        add_action('admin_menu', [$this, 'restrict_woocommerce_menus'], 999);
        add_action('admin_menu', [$this, 'remove_payments_menu'], 9999);
        add_action('admin_init', [$this, 'setup_woocommerce_capabilities']);
        add_action('admin_notices', [$this, 'admin_notices']);

        // Menü kısıtlama hook'ları
        add_action('admin_head', [$this, 'hide_restricted_menus_css']);
        add_filter('user_has_cap', [$this, 'filter_user_capabilities'], 10, 4);

        // Dil dosyalarını yükle
        add_action('plugins_loaded', [$this, 'load_textdomain']);

        // Admin init'te rol kontrolü yap
        add_action('admin_init', [$this, 'check_and_create_role']);
    }
    
    /**
     * Eklenti etkinleştirme
     */
    public function activate() {
        // Superole rolünü oluştur
        $this->create_superole_role();

        // WordPress capabilities cache'ini temizle
        if (function_exists('wp_cache_delete')) {
            wp_cache_delete('user_roles', 'options');
        }

        // Flush rewrite rules
        flush_rewrite_rules();

        // Aktivasyon zamanını kaydet
        update_option('role_custom_activated', current_time('mysql'));

        // Başarı bildirimi için transient ayarla
        set_transient('role_custom_activated_notice', true, 30);

        // Debug için log
        error_log('Role Custom: Eklenti etkinleştirildi ve Superole rolü oluşturuldu.');
    }
    
    /**
     * Eklenti devre dışı bırakma
     */
    public function deactivate() {
        $this->remove_superole_role();
        
        // Flush rewrite rules
        flush_rewrite_rules();
        
        // Deaktivasyon zamanını kaydet
        update_option('role_custom_deactivated', current_time('mysql'));
    }
    
    /**
     * Superole rolünü oluştur
     */
    private function create_superole_role() {
        // Önce mevcut rolü kaldır (eğer varsa)
        remove_role(self::SUPEROLE_ROLE);

        // Mevcut subscriber rolünün yetkilerini al
        $subscriber = get_role('subscriber');
        $capabilities = $subscriber ? $subscriber->capabilities : [];

        // Temel WordPress yetkileri ekle
        $capabilities = array_merge($capabilities, [
            'read' => true,
            'upload_files' => true,
            'edit_posts' => false,
            'edit_pages' => false,
            'publish_posts' => false,
            'publish_pages' => false,
            'delete_posts' => false,
            'delete_pages' => false,
        ]);
        
        // Tutor LMS yetkileri ekle
        $tutor_capabilities = [
            'manage_tutor_instructor' => true,
            'manage_tutor' => true,              // Öğrenciler menüsü için gerekli
            'tutor_instructor' => true,
            'read_course' => true,
            'read_lesson' => true,
            'read_quiz' => true,
            'read_question' => true,
            'read_announcement' => true,
        ];
        
        // WooCommerce yetkileri ekle - Tam erişim
        $woocommerce_capabilities = [
            // Temel WooCommerce yetkileri
            'manage_woocommerce' => true,
            'view_woocommerce_reports' => true,

            // Sipariş yönetimi
            'edit_shop_orders' => true,
            'edit_others_shop_orders' => true,
            'publish_shop_orders' => true,
            'read_shop_orders' => true,
            'delete_shop_orders' => true,
            'delete_others_shop_orders' => true,
            'read_private_shop_orders' => true,
            'edit_private_shop_orders' => true,
            'delete_private_shop_orders' => true,

            // Ürün yönetimi
            'edit_products' => true,
            'edit_others_products' => true,
            'publish_products' => true,
            'read_products' => true,
            'delete_products' => true,
            'delete_others_products' => true,
            'read_private_products' => true,
            'edit_private_products' => true,
            'delete_private_products' => true,

            // Ürün kategorileri ve etiketleri
            'manage_product_terms' => true,
            'edit_product_terms' => true,
            'delete_product_terms' => true,
            'assign_product_terms' => true,

            // Kupon yönetimi
            'edit_shop_coupons' => true,
            'edit_others_shop_coupons' => true,
            'publish_shop_coupons' => true,
            'read_shop_coupons' => true,
            'delete_shop_coupons' => true,
            'delete_others_shop_coupons' => true,
            'read_private_shop_coupons' => true,
            'edit_private_shop_coupons' => true,
            'delete_private_shop_coupons' => true,

            // Webhook yönetimi
            'edit_shop_webhooks' => true,
            'edit_others_shop_webhooks' => true,
            'publish_shop_webhooks' => true,
            'read_shop_webhooks' => true,
            'delete_shop_webhooks' => true,
            'delete_others_shop_webhooks' => true,
            'read_private_shop_webhooks' => true,
            'edit_private_shop_webhooks' => true,
            'delete_private_shop_webhooks' => true,
        ];
        
        // Tüm yetkileri birleştir
        $all_capabilities = array_merge($capabilities, $tutor_capabilities, $woocommerce_capabilities);
        
        // Rolü ekle
        $result = add_role(
            self::SUPEROLE_ROLE,
            __('Superole', 'role-custom'),
            $all_capabilities
        );

        // WordPress roles cache'ini temizle
        if (function_exists('wp_roles')) {
            wp_roles()->reinit();
        }

        // Global $wp_roles değişkenini sıfırla
        global $wp_roles;
        if (isset($wp_roles)) {
            $wp_roles = null;
        }

        // Log kaydı
        if ($result) {
            error_log('Role Custom: Superole rolü başarıyla oluşturuldu.');
        } else {
            error_log('Role Custom: Superole rolü oluşturulamadı!');
        }
    }
    
    /**
     * Superole rolünü kaldır
     */
    private function remove_superole_role() {
        // Önce bu role sahip kullanıcıları subscriber yap
        $users = get_users(['role' => self::SUPEROLE_ROLE]);
        foreach ($users as $user) {
            $user->set_role('subscriber');
        }

        // Rolü kaldır
        remove_role(self::SUPEROLE_ROLE);

        // WordPress roles cache'ini temizle
        if (function_exists('wp_roles')) {
            wp_roles()->reinit();
        }

        // Global $wp_roles değişkenini sıfırla
        global $wp_roles;
        if (isset($wp_roles)) {
            $wp_roles = null;
        }

        // Log kaydı
        error_log('Role Custom: Superole rolü kaldırıldı.');
    }
    
    /**
     * Tutor LMS menülerini kısıtla
     */
    public function restrict_tutor_menus() {
        // Sadece Superole rolündeki kullanıcılar için
        if (!$this->is_current_user_superole()) {
            return;
        }
        
        global $submenu;
        
        // Tutor LMS alt menülerini kontrol et
        if (isset($submenu['tutor'])) {
            $allowed_slugs = $this->get_allowed_tutor_menu_slugs();
            
            foreach ($submenu['tutor'] as $key => $menu_item) {
                $menu_slug = $menu_item[2];
                
                // İzin verilen menüler listesinde değilse kaldır
                if (!in_array($menu_slug, $allowed_slugs)) {
                    unset($submenu['tutor'][$key]);
                }
            }
        }
    }
    
    /**
     * İzin verilen Tutor LMS menü slug'larını döndür
     */
    private function get_allowed_tutor_menu_slugs() {
        // Tutor LMS admin menü analizi sonucu doğru slug'lar
        return [
            'tutor',                           // Kurslar (ana sayfa)
            'tutor-students',                  // Öğrenciler (Students_List::STUDENTS_LIST_PAGE)
            'tutor_announcements',             // Duyurular
            'question_answer',                 // Q&A (Question_Answers_List::QUESTION_ANSWER_PAGE)
            'tutor_quiz_attempts'              // Sınav Denemeleri (Quiz_Attempts_List::QUIZ_ATTEMPT_PAGE)
        ];
    }

    /**
     * WooCommerce menülerini kısıtla
     */
    public function restrict_woocommerce_menus() {
        // Sadece Superole rolündeki kullanıcılar için
        if (!$this->is_current_user_superole()) {
            return;
        }

        global $menu, $submenu;

        // 1. Ödemeler ana sekmesini gizle (farklı olası slug'lar)
        $payment_menu_slugs = [
            'wc-admin&path=/payments/connect',
            'admin.php?page=wc-admin&path=/payments/connect',
            'wc-admin&path=/payments',
            'admin.php?page=wc-admin&path=/payments',
            'woocommerce-payments',
            'wc-payments',
            'admin.php?page=wc-settings&tab=checkout',
            'wc-settings&tab=checkout'
        ];

        foreach ($payment_menu_slugs as $slug) {
            $this->remove_top_level_menu($slug);
            // WordPress'in remove_menu_page fonksiyonunu da kullan
            remove_menu_page($slug);
        }

        // Spesifik ödemeler menüsünü kaldır
        remove_menu_page('admin.php?page=wc-settings&tab=checkout');
        remove_menu_page('wc-settings&tab=checkout');

        // 2. WooCommerce alt menülerini kısıtla
        if (isset($submenu['woocommerce'])) {
            $restricted_wc_slugs = $this->get_restricted_woocommerce_menu_slugs();

            foreach ($submenu['woocommerce'] as $key => $menu_item) {
                $menu_slug = $menu_item[2];

                // Kısıtlı menüler listesinde varsa kaldır
                if (in_array($menu_slug, $restricted_wc_slugs)) {
                    unset($submenu['woocommerce'][$key]);
                }
            }
        }
    }

    /**
     * Kısıtlı WooCommerce menü slug'larını döndür
     */
    private function get_restricted_woocommerce_menu_slugs() {
        return [
            'wc-settings',                     // Ayarlar
            'wc-status',                       // Durum
            'wc-addons',                       // Genişletme Paketleri
        ];
    }

    /**
     * Top-level menüyü kaldır
     */
    private function remove_top_level_menu($menu_slug) {
        global $menu;

        foreach ($menu as $key => $menu_item) {
            if (isset($menu_item[2]) && $menu_item[2] === $menu_slug) {
                unset($menu[$key]);
                break;
            }
        }
    }

    /**
     * Ödemeler menüsünü özel olarak kaldır
     */
    public function remove_payments_menu() {
        // Sadece Superole rolündeki kullanıcılar için
        if (!$this->is_current_user_superole()) {
            return;
        }

        global $menu;

        // Tüm menüleri kontrol et ve ödemeler ile ilgili olanları kaldır
        foreach ($menu as $key => $menu_item) {
            if (isset($menu_item[2])) {
                $menu_slug = $menu_item[2];
                $menu_title = isset($menu_item[0]) ? $menu_item[0] : '';

                // Menü slug'ında veya başlığında ödemeler/payments geçiyorsa kaldır
                if (
                    strpos($menu_slug, 'payments') !== false ||
                    strpos($menu_slug, 'checkout') !== false ||
                    strpos($menu_slug, 'PAYMENTS_MENU_ITEM') !== false ||
                    strpos($menu_title, 'Ödemeler') !== false ||
                    strpos($menu_title, 'Payments') !== false
                ) {
                    unset($menu[$key]);
                }
            }
        }

        // WordPress'in remove_menu_page fonksiyonunu da kullan
        $payment_pages = [
            'admin.php?page=wc-settings&tab=checkout',
            'wc-settings&tab=checkout',
            'woocommerce-payments',
            'wc-payments'
        ];

        foreach ($payment_pages as $page) {
            remove_menu_page($page);
        }
    }
    
    /**
     * Mevcut kullanıcının Superole rolünde olup olmadığını kontrol et
     */
    private function is_current_user_superole() {
        $current_user = wp_get_current_user();
        return in_array(self::SUPEROLE_ROLE, $current_user->roles);
    }
    
    /**
     * WooCommerce ve Tutor LMS yetkilerini ayarla
     */
    public function setup_woocommerce_capabilities() {
        // Superole rolü için WooCommerce ve Tutor LMS yetkilerini kontrol et ve gerekirse ekle
        $role = get_role(self::SUPEROLE_ROLE);
        if ($role) {
            // Tutor LMS yetkileri
            $tutor_caps = [
                'manage_tutor_instructor',
                'manage_tutor',              // Öğrenciler menüsü için gerekli
                'tutor_instructor',
                'read_course',
                'read_lesson',
                'read_quiz',
                'read_question',
                'read_announcement',
            ];

            // WooCommerce yetkileri
            $woocommerce_caps = [
                // Temel WooCommerce yetkileri
                'manage_woocommerce',
                'view_woocommerce_reports',

                // Sipariş yönetimi
                'edit_shop_orders',
                'edit_others_shop_orders',
                'publish_shop_orders',
                'read_shop_orders',
                'delete_shop_orders',
                'delete_others_shop_orders',
                'read_private_shop_orders',
                'edit_private_shop_orders',
                'delete_private_shop_orders',

                // Ürün yönetimi
                'edit_products',
                'edit_others_products',
                'publish_products',
                'read_products',
                'delete_products',
                'delete_others_products',
                'read_private_products',
                'edit_private_products',
                'delete_private_products',

                // Ürün kategorileri ve etiketleri
                'manage_product_terms',
                'edit_product_terms',
                'delete_product_terms',
                'assign_product_terms',

                // Kupon yönetimi
                'edit_shop_coupons',
                'edit_others_shop_coupons',
                'publish_shop_coupons',
                'read_shop_coupons',
                'delete_shop_coupons',
                'delete_others_shop_coupons',
                'read_private_shop_coupons',
                'edit_private_shop_coupons',
                'delete_private_shop_coupons',

                // Webhook yönetimi
                'edit_shop_webhooks',
                'edit_others_shop_webhooks',
                'publish_shop_webhooks',
                'read_shop_webhooks',
                'delete_shop_webhooks',
                'delete_others_shop_webhooks',
                'read_private_shop_webhooks',
                'edit_private_shop_webhooks',
                'delete_private_shop_webhooks',
            ];

            // Tüm yetkileri ekle
            $all_caps = array_merge($tutor_caps, $woocommerce_caps);
            foreach ($all_caps as $cap) {
                if (!$role->has_cap($cap)) {
                    $role->add_cap($cap);
                }
            }
        }
    }
    
    /**
     * Kısıtlı menüleri gizlemek için CSS
     */
    public function hide_restricted_menus_css() {
        if (!$this->is_current_user_superole()) {
            return;
        }

        // Tutor LMS ve WooCommerce menuleri icin CSS
        echo '<style>
            /* Tutor LMS restricted menus - Hide for Superole role */
            #adminmenu .wp-submenu li a[href*="tutor-new-feature"],
            #adminmenu .wp-submenu li a[href*="create-course"],
            #adminmenu .wp-submenu li a[href*="tutor-themes"],
            #adminmenu .wp-submenu li a[href*="course-category"],
            #adminmenu .wp-submenu li a[href*="course-tag"],
            #adminmenu .wp-submenu li a[href*="tutor-instructors"],
            #adminmenu .wp-submenu li a[href*="tutor-addons"],
            #adminmenu .wp-submenu li a[href*="tutor-tools"],
            #adminmenu .wp-submenu li a[href*="tutor_settings"],
            #adminmenu .wp-submenu li a[href*="tutor-get-pro"],
            #adminmenu .wp-submenu li a[href*="tutor_orders"],
            #adminmenu .wp-submenu li a[href*="tutor_coupons"],

            /* WooCommerce restricted menus - Hide for Superole role */
            #adminmenu .wp-submenu li a[href*="wc-settings"],
            #adminmenu .wp-submenu li a[href*="wc-status"],
            #adminmenu .wp-submenu li a[href*="wc-addons"],

            /* Hide Payments main menu - Various possible slugs */
            #adminmenu li a[href*="payments/connect"],
            #adminmenu li a[href*="wc-admin&path=/payments"],
            #adminmenu li a[href*="woocommerce-payments"],
            #adminmenu li a[href*="wc-payments"],
            #adminmenu li a[href*="wc-settings&tab=checkout"],
            #adminmenu li.toplevel_page_wc-admin-path-payments,
            #adminmenu li[id*="payments"],
            #toplevel_page_admin-page-wc-settings-tab-checkout-from-PAYMENTS_MENU_ITEM,
            #adminmenu #toplevel_page_admin-page-wc-settings-tab-checkout-from-PAYMENTS_MENU_ITEM,
            li[id*="checkout"],
            li[id*="PAYMENTS_MENU_ITEM"],
            #adminmenu li[class*="checkout"],
            #adminmenu li[class*="payments"] {
                display: none !important;
            }
        </style>';

        // JavaScript ile dinamik olarak ödemeler menüsünü gizle
        echo '<script>
            jQuery(document).ready(function($) {
                // Ödemeler menüsünü gizle
                $("#toplevel_page_admin-page-wc-settings-tab-checkout-from-PAYMENTS_MENU_ITEM").hide();
                $("li[id*=\"checkout\"]").hide();
                $("li[id*=\"PAYMENTS_MENU_ITEM\"]").hide();
                $("li[id*=\"payments\"]").hide();
                $("a[href*=\"wc-settings&tab=checkout\"]").parent().hide();
                $("a[href*=\"payments\"]").parent().hide();

                // Menü metni "Ödemeler" olan tüm menüleri gizle
                $("#adminmenu a").each(function() {
                    if ($(this).text().trim() === "Ödemeler" || $(this).text().trim() === "Payments") {
                        $(this).parent().hide();
                    }
                });
            });
        </script>';
    }
    
    /**
     * Kullanıcı yetkilerini filtrele
     */
    public function filter_user_capabilities($allcaps, $caps, $args, $user) {
        // Sadece Superole rolündeki kullanıcılar için
        if (!in_array(self::SUPEROLE_ROLE, $user->roles)) {
            return $allcaps;
        }

        // Tutor LMS ve WooCommerce yetkilerini garanti et
        $guaranteed_caps = [
            // Tutor LMS yetkileri
            'manage_tutor_instructor' => true,
            'manage_tutor' => true,              // Öğrenciler menüsü için gerekli
            'tutor_instructor' => true,

            // WooCommerce yetkileri - Tam erişim
            'manage_woocommerce' => true,
            'view_woocommerce_reports' => true,
            'edit_shop_orders' => true,
            'edit_others_shop_orders' => true,
            'edit_products' => true,
            'edit_others_products' => true,
            'manage_product_terms' => true,
            'edit_shop_coupons' => true,
            'edit_others_shop_coupons' => true,
        ];

        return array_merge($allcaps, $guaranteed_caps);
    }
    
    /**
     * Admin bildirimleri
     */
    public function admin_notices() {
        // Eklenti etkinleştirildikten sonra başarı mesajı
        if (get_transient('role_custom_activated_notice')) {
            $role_exists = get_role(self::SUPEROLE_ROLE) ? 'Evet' : 'Hayır';
            echo '<div class="notice notice-success is-dismissible">';
            echo '<p><strong>' . __('Role Custom:', 'role-custom') . '</strong> ';
            echo __('Eklenti başarıyla etkinleştirildi. Superole rolü oluşturuldu.', 'role-custom');
            echo '<br><small>Debug: Superole rolü mevcut mu? ' . $role_exists . '</small>';
            echo '</p></div>';
            delete_transient('role_custom_activated_notice');
        }

        // Superole rolü eksikse hata mesajı
        if (!get_role(self::SUPEROLE_ROLE) && current_user_can('manage_options')) {
            echo '<div class="notice notice-error">';
            echo '<p><strong>' . __('Role Custom:', 'role-custom') . '</strong> ';
            echo __('Superole rolü bulunamadı! Eklentiyi devre dışı bırakıp tekrar etkinleştirin.', 'role-custom');
            echo ' <a href="' . admin_url('plugins.php') . '">Eklentiler sayfasına git</a>';
            echo '</p></div>';
        }

        // Tutor LMS veya WooCommerce eksikse uyarı
        if (!class_exists('TUTOR\Tutor')) {
            echo '<div class="notice notice-warning">';
            echo '<p><strong>' . __('Role Custom:', 'role-custom') . '</strong> ';
            echo __('Tutor LMS eklentisi bulunamadı. Menü kısıtlamaları çalışmayabilir.', 'role-custom');
            echo '</p></div>';
        }

        if (!class_exists('WooCommerce')) {
            echo '<div class="notice notice-warning">';
            echo '<p><strong>' . __('Role Custom:', 'role-custom') . '</strong> ';
            echo __('WooCommerce eklentisi bulunamadı. WooCommerce yetkileri çalışmayabilir.', 'role-custom');
            echo '</p></div>';
        }
    }

    /**
     * Rol kontrolü ve gerekirse oluşturma
     */
    public function check_and_create_role() {
        // Sadece admin kullanıcılar için ve sadece bir kez çalıştır
        if (!current_user_can('manage_options')) {
            return;
        }

        // Eğer rol yoksa oluştur
        if (!get_role(self::SUPEROLE_ROLE)) {
            $this->create_superole_role();

            // Bildirim ekle
            add_action('admin_notices', function() {
                echo '<div class="notice notice-info is-dismissible">';
                echo '<p><strong>Role Custom:</strong> Superole rolü eksikti ve yeniden oluşturuldu.</p>';
                echo '</div>';
            });
        }
    }

    /**
     * Dil dosyalarını yükle
     */
    public function load_textdomain() {
        load_plugin_textdomain(
            'role-custom',
            false,
            dirname(ROLE_CUSTOM_PLUGIN_BASENAME) . '/languages'
        );
    }
}

// Eklentiyi başlat
function role_custom_init() {
    return Role_Custom::get_instance();
}

// WordPress yüklendikten sonra eklentiyi başlat
add_action('plugins_loaded', 'role_custom_init');

// Eklenti bilgilerini döndüren yardımcı fonksiyon
function role_custom() {
    return Role_Custom::get_instance();
}

// Eklenti etkinleştirme/devre dışı bırakma hook'ları
register_activation_hook(ROLE_CUSTOM_PLUGIN_FILE, 'role_custom_activate');
register_deactivation_hook(ROLE_CUSTOM_PLUGIN_FILE, 'role_custom_deactivate');

/**
 * Eklenti etkinleştirme fonksiyonu
 */
function role_custom_activate() {
    $role_custom = Role_Custom::get_instance();
    $role_custom->activate();
}

/**
 * Eklenti devre dışı bırakma fonksiyonu
 */
function role_custom_deactivate() {
    $role_custom = Role_Custom::get_instance();
    $role_custom->deactivate();
}

// Test dosyasını dahil et (sadece debug modunda)
if (defined('WP_DEBUG') && WP_DEBUG && is_admin()) {
    require_once ROLE_CUSTOM_PLUGIN_DIR . 'tests/test-role-custom.php';
}
