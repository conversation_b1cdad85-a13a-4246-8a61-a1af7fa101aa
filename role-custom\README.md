# Role Custom WordPress Eklentisi

## A<PERSON><PERSON><PERSON><PERSON>

Role Custom, WordPress için geliştirilmiş özel bir rol yönetimi eklentisidir. Bu eklenti "Superole" adında yeni bir kullanıcı rolü oluşturur ve bu rol için özel yetkilendirmeler sağlar.

## Özellikler

### 1. Superole Rolü
- Eklenti etkinleştirildiğinde otomatik olarak "Superole" rolü oluşturulur
- Eklenti devre dışı bırakıldığında rol güvenli bir şekilde kaldırılır
- Bu role sahip kullanıcılar otomatik olarak "subscriber" rolüne dönüştürülür

### 2. Tutor LMS Tam Instructor Yetkileri ⭐ YENİ!
Superole rolündeki kullanıcılar artık **Tutor Instructor rolündeki tüm yetkilere** sahiptir:

**🔑 Tutor LMS Yetkileri (Instructor Rolünden):**
- **Temel WordPress Yetkileri**: `edit_posts`, `read`, `upload_files`
- **<PERSON><PERSON><PERSON><PERSON>**: `manage_tutor`, `manage_tutor_instructor`, `tutor_instructor`
- **Kurs Yetkileri**: Kurs oluşturma, düzenleme, silme, yayınlama (`edit_tutor_course`, `publish_tutor_courses`)
- **Ders Yetkileri**: Ders oluşturma, düzenleme, silme, yayınlama (`edit_tutor_lesson`, `publish_tutor_lessons`)
- **Quiz Yetkileri**: Quiz oluşturma, düzenleme, silme, yayınlama (`edit_tutor_quiz`, `publish_tutor_quizzes`)
- **Soru Yetkileri**: Soru oluşturma, düzenleme, silme, yayınlama (`edit_tutor_question`, `publish_tutor_questions`)

**📋 Erişilebilir Tutor LMS Menüleri:**
- **Kurslar** (Ana sayfa - tam kurs yönetimi)
- **Öğrenciler** (Öğrenci yönetimi)
- **Duyurular** (Kurs duyuruları)
- **Q&A** (Soru-cevap yönetimi)
- **Sınav Denemeleri** (Quiz sonuçları)
- **Tüm diğer instructor menüleri** (Araçlar ve Ayarlar hariç)

**🚫 Gizlenen Admin Menüleri:**
- Tools (Araçlar)
- Settings (Ayarlar)
- **Para Çekme Talepleri** (Withdrawals) ← **YENİ!**
- Upgrade to Pro

### 3. WooCommerce Özelleştirilmiş Menü Yapısı
Superole rolündeki kullanıcılar için WooCommerce menü yapısı tamamen yeniden düzenlenmiştir:

**Görünür Ana Menüler:**
- **Ürünler** (Tüm ürün yönetimi)
- **Siparişler** ← **YENİ ANA MENÜ!** (`admin.php?page=wc-orders`)
- **Müşteriler** ← **YENİ ANA MENÜ!** (`users.php?role=customer`)
- **Raporlar** ← **YENİ ANA MENÜ!** (`admin.php?page=wc-reports`)
- **Analiz** (Raporlar ve analizler)
- **Pazarlama** (Pazarlama araçları)

**Tamamen Gizlenen Menüler:**
- **WooCommerce** (Ana sekme tamamen gizli)
- **Ödemeler** (Ana sekme tamamen gizli)
- **Araçlar** (WordPress ana sekmesi) ← **YENİ!**
- **Ayarlar** (WooCommerce alt menüsünde)
- **Durum** (WooCommerce alt menüsünde)
- **Genişletme Paketleri** (WooCommerce alt menüsünde)

## Gereksinimler

- WordPress 5.0 veya üzeri
- PHP 7.4 veya üzeri
- Tutor LMS eklentisi (menü kısıtlamaları için)
- WooCommerce eklentisi (e-ticaret özellikleri için)

## Kurulum

1. `role-custom` klasörünü WordPress'in `/wp-content/plugins/` dizinine yükleyin
2. WordPress admin panelinde "Eklentiler" bölümüne gidin
3. "Role Custom" eklentisini bulun ve "Etkinleştir" butonuna tıklayın
4. Eklenti etkinleştirildikten sonra "Superole" rolü otomatik olarak oluşturulacaktır

## Kullanım

### Superole Rolü Atama
1. WordPress admin panelinde "Kullanıcılar" > "Tüm Kullanıcılar" bölümüne gidin
2. Düzenlemek istediğiniz kullanıcıyı seçin
3. "Rol" dropdown menüsünden "Superole" seçin
4. "Kullanıcıyı Güncelle" butonuna tıklayın

### Yetkilerin Kontrolü
Superole rolüne sahip bir kullanıcı ile giriş yaparak:
- Tutor LMS menülerinin kısıtlandığını
- WooCommerce'e tam erişim olduğunu
- Sadece izin verilen menülerin görüntülendiğini kontrol edebilirsiniz

## Teknik Detaylar

### Hook'lar ve Filtreler
- `admin_menu` (priority: 999) - Tutor LMS menü kısıtlamaları
- `admin_init` - WooCommerce yetkilerinin ayarlanması
- `admin_notices` - Bildirim mesajları
- `admin_head` - CSS ile menü gizleme
- `user_has_cap` - Kullanıcı yetkilerinin filtrelenmesi

### Güvenlik
- Doğrudan dosya erişimi engellendi
- Tüm kullanıcı girişleri sanitize edildi
- WordPress nonce sistemi kullanıldı
- Yetki kontrolleri her adımda yapıldı

### Performans
- Minimum veritabanı sorgusu
- Efficient hook kullanımı
- Transient cache sistemi
- Lazy loading uygulandı

## Sorun Giderme

### Eklenti Etkinleştirme Sorunları
- WordPress ve PHP sürümlerini kontrol edin
- Diğer eklentilerle çakışma olup olmadığını kontrol edin
- WordPress debug modunu etkinleştirin

### Menü Kısıtlamaları Çalışmıyor
- Tutor LMS eklentisinin aktif olduğundan emin olun
- Kullanıcının Superole rolüne sahip olduğunu kontrol edin
- Tarayıcı cache'ini temizleyin

### WooCommerce Yetkileri Çalışmıyor
- WooCommerce eklentisinin aktif olduğundan emin olun
- Kullanıcının Superole rolüne sahip olduğunu kontrol edin
- WordPress yetkilerini yeniden yükleyin

## Kaldırma

1. WordPress admin panelinde "Eklentiler" bölümüne gidin
2. "Role Custom" eklentisini bulun ve "Devre Dışı Bırak" butonuna tıklayın
3. Eklenti devre dışı bırakıldığında:
   - Superole rolü otomatik olarak kaldırılır
   - Bu role sahip kullanıcılar "subscriber" rolüne dönüştürülür
   - Eklenti ayarları temizlenir

## Changelog

### 1.0.0
- İlk sürüm
- Superole rolü oluşturma/kaldırma sistemi
- Tutor LMS menü kısıtlama sistemi
- WooCommerce tam erişim sistemi
- Admin bildirim sistemi
- Çoklu dil desteği hazırlığı

## Destek

Bu eklenti ile ilgili sorularınız için:
- GitHub Issues bölümünü kullanın
- WordPress.org destek forumlarına başvurun
- Geliştirici ile iletişime geçin

## Lisans

Bu eklenti GPL v2 veya üzeri lisansı altında dağıtılmaktadır.

## Geliştirici

Role Custom Developer
- Website: https://example.com
- Email: <EMAIL>

---

**Not:** Bu eklenti WordPress standartlarına uygun olarak geliştirilmiştir ve sürekli güncellenmektedir.
